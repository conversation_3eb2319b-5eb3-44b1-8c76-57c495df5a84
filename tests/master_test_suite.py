"""
Master Test Suite for GrowthHive API
Comprehensive test suite covering all endpoints with and without authentication
"""

import pytest_asyncio
from httpx import AsyncClient, ASGITransport
from app.main import app
import uuid
from datetime import datetime
from typing import Dict, Any


class MasterTestSuite:
    """Comprehensive test suite for all GrowthHive API endpoints"""
    
    def __init__(self):
        self.base_url = "http://test"
        self.test_user_data = {
            "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!",
            "mobile": "+1234567890",  # Use valid mobile format
            "first_name": "Test",
            "last_name": "User",
            "role": "ADMIN"
        }
        self.auth_headers = {}
        self.test_data = {}
        
    def setup_client(self) -> AsyncClient:
        """Setup async client for testing"""
        transport = ASGITransport(app=app)
        return AsyncClient(transport=transport, base_url=self.base_url)

    def safe_json_response(self, response):
        """Safely parse JSON response or return text"""
        try:
            if response.status_code < 500 and response.content:
                return response.json()
            else:
                return response.text
        except Exception:
            return response.text
    
    async def register_and_login(self, client: AsyncClient) -> Dict[str, str]:
        """Register a test user and login to get auth headers"""
        print(f"\n🔐 Registering test user: {self.test_user_data['email']}")
        
        # Register user
        register_response = await client.post("/api/auth/register", json=self.test_user_data)
        print(f"Registration Status: {register_response.status_code}")
        if register_response.status_code not in [200, 201]:
            print(f"Registration failed: {register_response.text}")
            
        # Login user
        login_data = {
            "email_or_mobile": self.test_user_data["email"],
            "password": self.test_user_data["password"],
            "remember_me": False
        }
        
        login_response = await client.post("/api/auth/login", json=login_data)
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_json = self.safe_json_response(login_response)
            if isinstance(login_json, dict) and login_json.get("data") and login_json["data"].get("details"):
                token = login_json["data"]["details"]["access_token"]
                self.auth_headers = {"Authorization": f"Bearer {token}"}
                print("✅ Authentication successful")
                return self.auth_headers
        
        print(f"Login failed: {login_response.text}")
        return {}
    
    async def test_authentication_endpoints(self, client: AsyncClient):
        """Test all authentication endpoints"""
        print("\n" + "="*50)
        print("🔐 TESTING AUTHENTICATION ENDPOINTS")
        print("="*50)
        
        results = {}
        
        # Test Registration (no auth required)
        print("\n📝 Testing Registration...")
        unique_email = f"reg_test_{uuid.uuid4().hex[:8]}@example.com"
        register_data = {
            **self.test_user_data,
            "email": unique_email,
            "mobile": "+1987654321"  # Use valid mobile format
        }
        
        response = await client.post("/api/auth/register", json=register_data)
        results["register"] = {
            "status_code": response.status_code,
            "success": response.status_code in [200, 201],
            "response": self.safe_json_response(response)
        }
        print(f"Registration: {'✅ PASS' if results['register']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Test Login (no auth required)
        print("\n🔑 Testing Login...")
        login_data = {
            "email_or_mobile": unique_email,
            "password": register_data["password"],
            "remember_me": False
        }
        
        response = await client.post("/api/auth/login", json=login_data)
        results["login"] = {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "response": self.safe_json_response(response)
        }
        print(f"Login: {'✅ PASS' if results['login']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Get token for authenticated tests
        if results["login"]["success"]:
            login_json = self.safe_json_response(response)
            if isinstance(login_json, dict) and login_json.get("data") and login_json["data"].get("details"):
                token = login_json["data"]["details"]["access_token"]
                temp_headers = {"Authorization": f"Bearer {token}"}
                
                # Test Get Current User (auth required)
                print("\n👤 Testing Get Current User...")
                response = await client.get("/api/auth/me", headers=temp_headers)
                results["get_me"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": self.safe_json_response(response)
                }
                print(f"Get Me: {'✅ PASS' if results['get_me']['success'] else '❌ FAIL'} ({response.status_code})")
                
                # Test Logout (auth required)
                print("\n🚪 Testing Logout...")
                response = await client.post("/api/auth/logout", headers=temp_headers)
                results["logout"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": self.safe_json_response(response)
                }
                print(f"Logout: {'✅ PASS' if results['logout']['success'] else '❌ FAIL'} ({response.status_code})")
        
        return results
    
    async def test_categories_endpoints(self, client: AsyncClient):
        """Test all categories endpoints"""
        print("\n" + "="*50)
        print("📂 TESTING CATEGORIES ENDPOINTS")
        print("="*50)
        
        results = {}
        
        # Test without auth first
        print("\n🚫 Testing Categories without Authentication...")
        
        # List categories without auth
        response = await client.get("/api/categories")
        results["list_categories_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Categories (No Auth): {'✅ PASS' if results['list_categories_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")
        
        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Categories with Authentication...")
            
            # List categories with auth
            response = await client.get("/api/categories", headers=self.auth_headers)
            results["list_categories_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Categories (Auth): {'✅ PASS' if results['list_categories_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create category
            category_data = {
                "name": f"Test Category {uuid.uuid4().hex[:8]}",
                "description": "Test category description"
            }
            response = await client.post("/api/categories", json=category_data, headers=self.auth_headers)
            results["create_category"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Category: {'✅ PASS' if results['create_category']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store category ID for further tests
            if results["create_category"]["success"]:
                category_response = self.safe_json_response(response)
                if isinstance(category_response, dict) and category_response.get("data") and category_response["data"].get("id"):
                    self.test_data["category_id"] = category_response["data"]["id"]

                    # Get category by ID
                    response = await client.get(f"/api/categories/{self.test_data['category_id']}", headers=self.auth_headers)
                    results["get_category"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Category: {'✅ PASS' if results['get_category']['success'] else '❌ FAIL'} ({response.status_code})")
        
        return results

    async def test_subcategories_endpoints(self, client: AsyncClient):
        """Test all subcategories endpoints"""
        print("\n" + "="*50)
        print("📁 TESTING SUBCATEGORIES ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Subcategories without Authentication...")

        # List subcategories without auth
        response = await client.get("/api/subcategories")
        results["list_subcategories_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Subcategories (No Auth): {'✅ PASS' if results['list_subcategories_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers and self.test_data.get("category_id"):
            print("\n🔐 Testing Subcategories with Authentication...")

            # List subcategories with auth
            response = await client.get("/api/subcategories", headers=self.auth_headers)
            results["list_subcategories_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Subcategories (Auth): {'✅ PASS' if results['list_subcategories_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create subcategory
            subcategory_data = {
                "name": f"Test Subcategory {uuid.uuid4().hex[:8]}",
                "description": "Test subcategory description",
                "category_id": self.test_data["category_id"]
            }
            response = await client.post("/api/subcategories", json=subcategory_data, headers=self.auth_headers)
            results["create_subcategory"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Subcategory: {'✅ PASS' if results['create_subcategory']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store subcategory ID for further tests
            if results["create_subcategory"]["success"]:
                subcategory_response = self.safe_json_response(response)
                if isinstance(subcategory_response, dict) and subcategory_response.get("data") and subcategory_response["data"].get("id"):
                    self.test_data["subcategory_id"] = subcategory_response["data"]["id"]

                    # Get subcategory by ID
                    response = await client.get(f"/api/subcategories/{self.test_data['subcategory_id']}", headers=self.auth_headers)
                    results["get_subcategory"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Subcategory: {'✅ PASS' if results['get_subcategory']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_franchisors_endpoints(self, client: AsyncClient):
        """Test all franchisor endpoints"""
        print("\n" + "="*50)
        print("🏢 TESTING FRANCHISORS ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Franchisors without Authentication...")

        # List franchisors without auth
        response = await client.get("/api/franchisors/")  # Add trailing slash
        results["list_franchisors_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Franchisors (No Auth): {'✅ PASS' if results['list_franchisors_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Franchisors with Authentication...")

            # List franchisors with auth
            response = await client.get("/api/franchisors", headers=self.auth_headers)
            results["list_franchisors_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Franchisors (Auth): {'✅ PASS' if results['list_franchisors_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create franchisor
            franchisor_data = {
                "name": f"Test Franchisor {uuid.uuid4().hex[:8]}",
                "category": "food_beverage",
                "region": "australia",
                "budget": 150000.0,
                "sub_category": "restaurant"
            }
            response = await client.post("/api/franchisors", json=franchisor_data, headers=self.auth_headers)
            results["create_franchisor"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Franchisor: {'✅ PASS' if results['create_franchisor']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store franchisor ID for further tests
            if results["create_franchisor"]["success"]:
                franchisor_response = self.safe_json_response(response)
                if isinstance(franchisor_response, dict) and franchisor_response.get("data") and franchisor_response["data"].get("id"):
                    self.test_data["franchisor_id"] = franchisor_response["data"]["id"]

                    # Get franchisor by ID
                    response = await client.get(f"/api/franchisors/{self.test_data['franchisor_id']}", headers=self.auth_headers)
                    results["get_franchisor"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Franchisor: {'✅ PASS' if results['get_franchisor']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def test_leads_endpoints(self, client: AsyncClient):
        """Test all leads endpoints"""
        print("\n" + "="*50)
        print("👥 TESTING LEADS ENDPOINTS")
        print("="*50)

        results = {}

        # Test without auth first
        print("\n🚫 Testing Leads without Authentication...")

        # List leads without auth
        response = await client.get("/api/leads")
        results["list_leads_no_auth"] = {
            "status_code": response.status_code,
            "success": response.status_code == 401,  # Should be unauthorized
            "response": self.safe_json_response(response)
        }
        print(f"List Leads (No Auth): {'✅ PASS' if results['list_leads_no_auth']['success'] else '❌ FAIL'} ({response.status_code})")

        # Test with auth
        if self.auth_headers:
            print("\n🔐 Testing Leads with Authentication...")

            # List leads with auth
            response = await client.get("/api/leads", headers=self.auth_headers)
            results["list_leads_auth"] = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": self.safe_json_response(response)
            }
            print(f"List Leads (Auth): {'✅ PASS' if results['list_leads_auth']['success'] else '❌ FAIL'} ({response.status_code})")

            # Create lead
            lead_data = {
                "full_name": f"Test Lead {uuid.uuid4().hex[:8]}",
                "contact_number": "+1555123456",  # Use valid mobile format
                "email": f"lead_{uuid.uuid4().hex[:8]}@example.com",
                "location": "Test City",
                "lead_source": "website",
                "franchise_preference": "food_beverage",
                "budget_preference": "100000-200000",
                "qualification_status": "new"
            }
            response = await client.post("/api/leads", json=lead_data, headers=self.auth_headers)
            results["create_lead"] = {
                "status_code": response.status_code,
                "success": response.status_code in [200, 201],
                "response": self.safe_json_response(response)
            }
            print(f"Create Lead: {'✅ PASS' if results['create_lead']['success'] else '❌ FAIL'} ({response.status_code})")

            # Store lead ID for further tests
            if results["create_lead"]["success"]:
                lead_response = self.safe_json_response(response)
                if isinstance(lead_response, dict) and lead_response.get("data") and lead_response["data"].get("id"):
                    self.test_data["lead_id"] = lead_response["data"]["id"]

                    # Get lead by ID
                    response = await client.get(f"/api/leads/{self.test_data['lead_id']}", headers=self.auth_headers)
                    results["get_lead"] = {
                        "status_code": response.status_code,
                        "success": response.status_code == 200,
                        "response": self.safe_json_response(response)
                    }
                    print(f"Get Lead: {'✅ PASS' if results['get_lead']['success'] else '❌ FAIL'} ({response.status_code})")

        return results

    async def run_all_tests(self):
        """Run all endpoint tests"""
        print("\n" + "="*70)
        print("🚀 GROWTHHIVE API MASTER TEST SUITE")
        print("="*70)
        print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        all_results = {}

        async with self.setup_client() as client:
            # Setup authentication
            await self.register_and_login(client)

            # Run all test suites
            all_results["authentication"] = await self.test_authentication_endpoints(client)
            all_results["categories"] = await self.test_categories_endpoints(client)
            all_results["subcategories"] = await self.test_subcategories_endpoints(client)
            all_results["franchisors"] = await self.test_franchisors_endpoints(client)
            all_results["leads"] = await self.test_leads_endpoints(client)

        # Print summary
        self.print_test_summary(all_results)
        return all_results

    def print_test_summary(self, results: Dict[str, Any]):
        """Print comprehensive test summary"""
        print("\n" + "="*70)
        print("📊 TEST SUMMARY")
        print("="*70)

        total_tests = 0
        passed_tests = 0

        for module, module_results in results.items():
            print(f"\n📂 {module.upper()} MODULE:")
            module_passed = 0
            module_total = 0

            for test_name, test_result in module_results.items():
                status = "✅ PASS" if test_result["success"] else "❌ FAIL"
                print(f"  {test_name}: {status} ({test_result['status_code']})")

                module_total += 1
                total_tests += 1

                if test_result["success"]:
                    module_passed += 1
                    passed_tests += 1

            print(f"  Module Summary: {module_passed}/{module_total} passed")

        print(f"\n🎯 OVERALL SUMMARY:")
        print(f"  Total Tests: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {total_tests - passed_tests}")
        print(f"  Success Rate: {(passed_tests/total_tests*100):.1f}%")

        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} TESTS FAILED")

        print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*70)


# Test execution functions
async def run_master_test_suite():
    """Run the complete master test suite"""
    suite = MasterTestSuite()
    return await suite.run_all_tests()


# Pytest integration
@pytest_asyncio.fixture
async def master_test_suite():
    """Pytest fixture for master test suite"""
    return MasterTestSuite()


@pytest_asyncio.fixture
async def run_full_test_suite(master_test_suite):
    """Pytest fixture to run full test suite"""
    return await master_test_suite.run_all_tests()


# Main execution
if __name__ == "__main__":
    import asyncio

    print("🚀 Running GrowthHive Master Test Suite...")
    results = asyncio.run(run_master_test_suite())

    # Exit with appropriate code
    total_tests = sum(len(module_results) for module_results in results.values())
    passed_tests = sum(
        sum(1 for test_result in module_results.values() if test_result["success"])
        for module_results in results.values()
    )

    exit_code = 0 if passed_tests == total_tests else 1
    exit(exit_code)
